import React from 'react';
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

export default function MigrationExample() {
  // ✅ Nova pattern - Multiple breakpoint queries
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
  const isSmallScreen = useMediaQuery(NovaTheme.breakpoints.down('md'));

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, maxWidth: 600 }}>
      <Typography variant="titleLarge">Migration from MUI to Nova</Typography>

      {/* Current Status */}
      <Card.Root>
        <Card.Content>
          <Typography variant="titleMedium" sx={{ mb: 2 }}>
            Current Breakpoint Status
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="bodyMedium">
              📱 Mobile (down sm): <strong>{isMobile ? 'Active' : 'Inactive'}</strong>
            </Typography>
            <Typography variant="bodyMedium">
              📟 Tablet (sm to md): <strong>{isTablet ? 'Active' : 'Inactive'}</strong>
            </Typography>
            <Typography variant="bodyMedium">
              🖥️ Desktop (up lg): <strong>{isDesktop ? 'Active' : 'Inactive'}</strong>
            </Typography>
          </Box>
        </Card.Content>
      </Card.Root>

      {/* Before/After Comparison */}
      <Box sx={{ display: 'grid', gridTemplateColumns: isSmallScreen ? '1fr' : '1fr 1fr', gap: 2 }}>
        {/* Old MUI Pattern */}
        <Card.Root>
          <Card.Content>
            <Typography variant="titleMedium" sx={{ mb: 2, color: 'var(--palette-error)' }}>
              ❌ Old MUI Pattern
            </Typography>
            <Box
              sx={{
                p: 2,
                backgroundColor: 'var(--palette-errorContainer)',
                borderRadius: 1,
                border: '1px solid var(--palette-error)',
              }}
            >
              <Typography variant="labelSmall" sx={{ fontFamily: 'monospace', whiteSpace: 'pre-line' }}>
                {`import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

function Component() {
  const theme = useTheme();
  const isMobile = useMediaQuery(
    theme.breakpoints.down('sm')
  );
  const isDesktop = useMediaQuery(
    theme.breakpoints.up('lg')
  );

  // This doesn't work with PigmentCSS!
  return <div>...</div>;
}`}
              </Typography>
            </Box>
          </Card.Content>
        </Card.Root>

        {/* New Nova Pattern */}
        <Card.Root>
          <Card.Content>
            <Typography variant="titleMedium" sx={{ mb: 2, color: 'var(--palette-primary)' }}>
              ✅ New Nova Pattern
            </Typography>
            <Box
              sx={{
                p: 2,
                backgroundColor: 'var(--palette-primaryContainer)',
                borderRadius: 1,
                border: '1px solid var(--palette-primary)',
              }}
            >
              <Typography variant="labelSmall" sx={{ fontFamily: 'monospace', whiteSpace: 'pre-line' }}>
                {`import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

function Component() {
  const isMobile = useMediaQuery(
    NovaTheme.breakpoints.down('sm')
  );
  const isDesktop = useMediaQuery(
    NovaTheme.breakpoints.up('lg')
  );

  // Works perfectly with PigmentCSS!
  return <div>...</div>;
}`}
              </Typography>
            </Box>
          </Card.Content>
        </Card.Root>
      </Box>

      {/* Key Differences */}
      <Card.Root>
        <Card.Content>
          <Typography variant="titleMedium" sx={{ mb: 2 }}>
            Key Migration Points
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box>
              <Typography variant="bodyMedium" sx={{ fontWeight: 'bold', mb: 1 }}>
                1. Import Changes
              </Typography>
              <Typography
                variant="bodySmall"
                sx={{
                  color: 'var(--palette-onSurfaceVariant)',
                }}
              >
                Replace MUI imports with Nova's useMediaQuery hook and static NovaTheme
              </Typography>
            </Box>
            <Box>
              <Typography variant="bodyMedium" sx={{ fontWeight: 'bold', mb: 1 }}>
                2. No useTheme() Hook
              </Typography>
              <Typography
                variant="bodySmall"
                sx={{
                  color: 'var(--palette-onSurfaceVariant)',
                }}
              >
                PigmentCSS doesn't support runtime theme access. Use static NovaTheme instead
              </Typography>
            </Box>
            <Box>
              <Typography variant="bodyMedium" sx={{ fontWeight: 'bold', mb: 1 }}>
                3. Same Breakpoint Methods
              </Typography>
              <Typography
                variant="bodySmall"
                sx={{
                  color: 'var(--palette-onSurfaceVariant)',
                }}
              >
                All familiar methods work: .up(), .down(), .between(), .only()
              </Typography>
            </Box>
          </Box>
        </Card.Content>
      </Card.Root>

      {/* Live Demo */}
      <Card.Root
        style={{
          backgroundColor: isMobile
            ? 'var(--palette-primaryContainer)'
            : isTablet
              ? 'var(--palette-secondaryContainer)'
              : 'var(--palette-tertiaryContainer)',
        }}
      >
        <Card.Content>
          <Typography variant="titleMedium" sx={{ mb: 1 }}>
            🎯 Live Responsive Demo
          </Typography>
          <Typography variant="bodyMedium">This card changes color based on your screen size:</Typography>
          <Typography variant="bodySmall" sx={{ mt: 1, fontStyle: 'italic' }}>
            {isMobile && '📱 Mobile: Primary color theme'}
            {isTablet && '📟 Tablet: Secondary color theme'}
            {isDesktop && '🖥️ Desktop: Tertiary color theme'}
          </Typography>
        </Card.Content>
      </Card.Root>
    </Box>
  );
}
